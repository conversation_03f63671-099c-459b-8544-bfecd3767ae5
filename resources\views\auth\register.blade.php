<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - MOTIVUS</title>
    <link href="{{ asset('css/motivus.css') }}" rel="stylesheet">
</head>
<body>
    <div class="mobile-container">
        <div class="auth-container">
            <div class="auth-card">
                <!-- Header -->
                <div class="auth-header">
                    <div class="auth-logo">MOTIVUS</div>
                    <h1 class="auth-title">Sign Up</h1>
                    <p class="auth-subtitle">Create your account to get started</p>
                </div>

                <!-- Error Messages -->
                @if ($errors->any())
                    <div class="alert alert-danger">
                        @foreach ($errors->all() as $error)
                            <div>{{ $error }}</div>
                        @endforeach
                    </div>
                @endif

                <!-- Register Form -->
                <form method="POST" action="{{ route('register') }}">
                    @csrf

                    <div class="form-group">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text"
                               id="name"
                               name="name"
                               class="form-input"
                               placeholder="Enter your full name"
                               value="{{ old('name') }}"
                               required
                               autofocus>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email"
                               id="email"
                               name="email"
                               class="form-input"
                               placeholder="Enter your email"
                               value="{{ old('email') }}"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel"
                               id="phone"
                               name="phone"
                               class="form-input"
                               placeholder="+254 700 000 000"
                               value="{{ old('phone') }}">
                    </div>

                    <!-- Role Selection -->
                    <div class="role-selection">
                        <label class="form-label">I want to:</label>
                        <div class="role-options">
                            <div class="role-option">
                                <input type="radio" id="renter" name="role" value="renter" {{ old('role') == 'renter' ? 'checked' : '' }} required>
                                <label for="renter">Rent Cars</label>
                            </div>
                            <div class="role-option">
                                <input type="radio" id="owner" name="role" value="owner" {{ old('role') == 'owner' ? 'checked' : '' }}>
                                <label for="owner">List My Car</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <input type="password"
                               id="password"
                               name="password"
                               class="form-input"
                               placeholder="Create a password"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="password_confirmation" class="form-label">Confirm Password</label>
                        <input type="password"
                               id="password_confirmation"
                               name="password_confirmation"
                               class="form-input"
                               placeholder="Confirm your password"
                               required>
                    </div>

                    <button type="submit" class="primary-btn">
                        CREATE ACCOUNT
                    </button>
                </form>

                <!-- Divider -->
                <div class="divider">
                    <span>or continue with</span>
                </div>

                <!-- Google Sign Up -->
                <a href="{{ url('/auth/redirect/google') }}" class="google-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Continue with Google
                </a>

                <!-- Footer -->
                <div class="auth-footer">
                    Already have an account? <a href="{{ route('login') }}">Sign in here</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
