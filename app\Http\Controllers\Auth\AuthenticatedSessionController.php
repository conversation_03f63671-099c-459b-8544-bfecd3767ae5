<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthenticatedSessionController extends Controller
{
    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request)
    {
        $request->authenticate();

        $request->session()->regenerate();

        // Clear any intended URL to prevent role-based redirect conflicts
        $request->session()->forget('url.intended');

        // Redirect based on user role - fetch user fresh with role relationship
        $user = User::with('role')->find(Auth::id());
        if ($user->isAdmin()) {
            return redirect('/admin/dashboard');
        } elseif ($user->isOwner()) {
            return redirect('/owner/dashboard');
        } else {
            return redirect('/vehicles');
        }
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request)
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }

    /**
     * Show the login form.
     */
    public function create()
    {
        return view('auth.login');
    }
}
