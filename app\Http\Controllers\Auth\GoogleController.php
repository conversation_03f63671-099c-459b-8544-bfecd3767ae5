<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class GoogleController extends Controller
{
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            $user = User::where('email', $googleUser->getEmail())->first();

            if (!$user) {
                // Get default renter role for Google OAuth users
                $renterRole = Role::where('name', 'renter')->first();

                if (!$renterRole) {
                    return redirect('/register')->withErrors(['error' => 'System error: Default role not found.']);
                }

                $user = User::create([
                    'name' => $googleUser->getName() ?? $googleUser->getNickname() ?? 'Google User',
                    'email' => $googleUser->getEmail(),
                    'role_id' => $renterRole->id,
                    'email_verified_at' => now(), // Google accounts are pre-verified
                    'password' => Hash::make(Str::random(24)),
                ]);
            }

            Auth::login($user, true);

            // Redirect based on user role
            if ($user->isAdmin()) {
                return redirect('/admin/dashboard');
            } elseif ($user->isOwner()) {
                return redirect('/owner/dashboard');
            } else {
                return redirect('/vehicles');
            }
        } catch (\Exception $e) {
            return redirect('/login')->withErrors(['error' => 'Google authentication failed. Please try again.']);
        }
    }
}
