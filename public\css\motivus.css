/* Motivus Custom Styles - Responsive Design */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #00e6ff, #00aadd);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Navigation Bar (Desktop Only) */
.desktop-nav {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
}

.nav-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #00d4ff;
    text-decoration: none;
}

.nav-links {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #00d4ff;
}

.nav-btn {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
    color: white;
    text-decoration: none;
}

@media (min-width: 768px) {
    .desktop-nav {
        display: block;
    }

    .homepage {
        padding-top: 100px;
    }
}

/* Responsive Container */
.mobile-container {
    max-width: 100%;
    margin: 0 auto;
    min-height: 100vh;
    background: #fff;
    position: relative;
}

/* Desktop/Laptop specific styling */
@media (min-width: 768px) {
    .mobile-container {
        max-width: 1200px;
        box-shadow: none;
    }
}

/* Homepage Styles */
.homepage {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px 30px 60px;
    position: relative;
    overflow: hidden;
}

/* Desktop/Laptop Homepage */
@media (min-width: 768px) {
    .homepage {
        flex-direction: row;
        align-items: center;
        padding: 60px 80px;
        gap: 60px;
    }

    .homepage-left {
        flex: 1;
        max-width: 500px;
    }

    .homepage-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
}

.homepage::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.logo-section {
    text-align: center;
    z-index: 2;
    position: relative;
    animation: fadeInUp 1s ease-out;
}

/* Desktop Logo Section */
@media (min-width: 768px) {
    .logo-section {
        text-align: left;
        margin-bottom: 40px;
    }
}

.logo {
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: 3px;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 300;
}

/* Desktop Logo Styling */
@media (min-width: 768px) {
    .logo {
        font-size: 4rem;
        margin-bottom: 20px;
    }

    .tagline {
        font-size: 1.2rem;
        margin-bottom: 40px;
    }
}

.car-section {
    text-align: center;
    z-index: 2;
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out 0.3s both;
}

.car-image {
    width: 280px;
    height: 160px;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05)), url('../images/hero-car.jpeg') center center;
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 20px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

/* Car Photo Styling */
.car-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
    transition: all 0.3s ease;
}

/* Fallback when image fails to load */
.car-image.fallback::after {
    content: '🚗';
    position: absolute;
    font-size: 3rem;
    opacity: 0.7;
    z-index: 1;
}

.car-image.fallback {
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
}

.car-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(0, 153, 204, 0.1));
    transition: all 0.3s ease;
    opacity: 0;
}

.car-image:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0, 212, 255, 0.3);
}

.car-image:hover::before {
    opacity: 1;
}

.car-image:hover .car-photo {
    transform: scale(1.05);
}

/* Desktop Car Image */
@media (min-width: 768px) {
    .car-image {
        width: 450px;
        height: 270px;
        margin: 40px 0;
        border-radius: 25px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    }
}

/* Large Desktop Car Image */
@media (min-width: 1200px) {
    .car-image {
        width: 550px;
        height: 330px;
        border-radius: 30px;
    }
}

.car-text {
    margin-top: 30px;
    text-align: center;
    animation: fadeInLeft 1s ease-out 0.6s both;
}

/* Desktop Car Text */
@media (min-width: 768px) {
    .car-text {
        text-align: left;
        margin-top: 0;
    }
}

.car-text h2 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
}

.car-text p {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 300;
}

/* Desktop Car Text */
@media (min-width: 768px) {
    .car-text h2 {
        font-size: 2.2rem;
        margin-bottom: 20px;
        line-height: 1.2;
    }

    .car-text p {
        font-size: 1.2rem;
    }
}

.cta-section {
    z-index: 2;
    position: relative;
    animation: fadeInUp 1s ease-out 0.9s both;
}

.get-started-btn {
    width: 100%;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
    border: none;
    padding: 18px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: block;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.get-started-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 212, 255, 0.4);
    color: white;
    text-decoration: none;
}

/* Desktop CTA Button */
@media (min-width: 768px) {
    .get-started-btn {
        max-width: 300px;
        padding: 20px 40px;
        font-size: 1.3rem;
        margin: 0 auto;
    }
}

/* Auth Pages Styles */
.auth-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
}

/* Desktop Auth Pages */
@media (min-width: 768px) {
    .auth-container {
        padding: 40px;
    }

    .auth-card {
        max-width: 500px;
        padding: 60px 50px;
        margin: 0;
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 40px;
}

.auth-logo {
    font-size: 2rem;
    font-weight: 700;
    color: #0099cc;
    margin-bottom: 10px;
}

.auth-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: #666;
    font-size: 0.9rem;
}

/* Desktop Auth Header */
@media (min-width: 768px) {
    .auth-logo {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }

    .auth-title {
        font-size: 2rem;
        margin-bottom: 12px;
    }

    .auth-subtitle {
        font-size: 1.1rem;
    }
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-input:focus {
    outline: none;
    border-color: #0099cc;
    box-shadow: 0 0 0 3px rgba(0, 153, 204, 0.1);
    transform: translateY(-1px);
}

.form-input::placeholder {
    color: #999;
}

/* Desktop Form Inputs */
@media (min-width: 768px) {
    .form-input {
        padding: 18px 20px;
        font-size: 1.1rem;
        border-radius: 15px;
    }

    .form-label {
        font-size: 1rem;
        margin-bottom: 10px;
    }

    .form-group {
        margin-bottom: 25px;
    }
}

/* Role Selection */
.role-selection {
    margin: 25px 0;
}

.role-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 15px;
}

/* Desktop Role Selection */
@media (min-width: 768px) {
    .role-options {
        gap: 20px;
        margin-top: 20px;
    }

    .role-option label {
        padding: 20px 15px;
        font-size: 1rem;
    }
}

.role-option {
    position: relative;
}

.role-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.role-option label {
    display: block;
    padding: 15px 10px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    background: #fff;
}

.role-option input[type="radio"]:checked + label {
    border-color: #0099cc;
    background: #f0f9ff;
    color: #0099cc;
}

.primary-btn {
    width: 100%;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
    border: none;
    padding: 18px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
    background: linear-gradient(45deg, #00e6ff, #00aadd);
}

/* Desktop Primary Button */
@media (min-width: 768px) {
    .primary-btn {
        padding: 20px 30px;
        font-size: 1.2rem;
        margin: 30px 0;
    }
}

.divider {
    text-align: center;
    margin: 25px 0;
    position: relative;
    color: #999;
    font-size: 0.9rem;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
    z-index: 1;
}

.divider span {
    background: white;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

.google-btn {
    width: 100%;
    background: white;
    color: #333;
    border: 2px solid #e1e5e9;
    padding: 15px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
}

.google-btn:hover {
    border-color: #ccc;
    background: #f8f9fa;
    color: #333;
    text-decoration: none;
}

.auth-footer {
    text-align: center;
    margin-top: 30px;
    color: #666;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #0099cc;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Verification Page Styles */
.verification-container {
    text-align: center;
    padding: 60px 30px;
}

.verification-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.verification-subtitle {
    color: #666;
    margin-bottom: 40px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.otp-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 40px 0;
}

.otp-input {
    width: 50px;
    height: 50px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    transition: all 0.3s ease;
}

.otp-input:focus {
    outline: none;
    border-color: #0099cc;
    box-shadow: 0 0 0 3px rgba(0, 153, 204, 0.1);
}

.resend-text {
    color: #666;
    font-size: 0.9rem;
    margin-top: 30px;
}

.resend-link {
    color: #0099cc;
    text-decoration: none;
    font-weight: 500;
}

.resend-link:hover {
    text-decoration: underline;
}

/* Alert Styles */
.alert {
    padding: 15px;
    border-radius: 12px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.alert-danger {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.alert-success {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

/* Responsive Design */

/* Large Desktop */
@media (min-width: 1200px) {
    .mobile-container {
        max-width: 1400px;
    }

    .homepage {
        padding: 80px 120px;
        gap: 100px;
    }

    .logo {
        font-size: 5rem !important;
    }

    .car-text h2 {
        font-size: 2.8rem !important;
    }

    .car-image {
        width: 500px !important;
        height: 300px !important;
    }

    .auth-card {
        max-width: 600px !important;
        padding: 80px 60px !important;
    }
}

/* Tablet */
@media (min-width: 768px) and (max-width: 1199px) {
    .homepage {
        padding: 60px;
        gap: 40px;
    }

    .auth-container {
        padding: 60px 40px;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .mobile-container {
        max-width: 100%;
    }

    .homepage {
        padding: 30px 20px 50px;
        flex-direction: column;
    }

    .car-image {
        width: 250px;
        height: 140px;
    }

    .auth-card {
        padding: 30px 20px;
    }

    .otp-input {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }

    .otp-container {
        gap: 10px;
    }

    .logo {
        font-size: 2rem !important;
    }

    .car-text h2 {
        font-size: 1.2rem !important;
    }
}
